<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { IVerse } from '@/types/types-quran.ts';
import VerseEndSign from '@/components/quran/VerseEndSign.vue';
import Api from '@/services/api/api.ts';

const props = defineProps<{
  number: number;
  content: IVerse[];
  highlightedVerseIndex?: number;
}>();

const verseEndSigns = ref<(typeof VerseEndSign)[]>([]);

const PHOTOS_REPOSITORY = 'https://file-keeper.com/media/image/quran-pages-2';
const PHOTOS_REPOSITORY_BACKUP = `${new Api().host}/quran-pages`;
// const PHOTOS_REPOSITORY = `https://behnegar.app/api/v1/tavoos/quran/page/\(number)/photo`;

const isLoading = ref(true);
const isLoaded = ref(false);

const activeRepository = ref(PHOTOS_REPOSITORY_BACKUP);

const src = computed(() => `${activeRepository.value}/quran-${String(props.number).padStart(3, '0')}.jpg`);
// const src = computed(() => `${PHOTOS_REPOSITORY}/${props.number}/photo`);

watch(src, (newSrc, oldSrc) => {
  if (newSrc === oldSrc) return;

  isLoaded.value = false;
  isLoading.value = true;
});

function onImageLoad() {
  isLoading.value = false;
  isLoaded.value = true;
}

function onImageError() {
  activeRepository.value = PHOTOS_REPOSITORY_BACKUP;
};

function collapseOtherOpenMenus(verse: IVerse) {
  verseEndSigns.value.forEach(sign => {
    if (sign.verse.indexInQuran === verse.indexInQuran) return;

    sign.collapseMenu();
  });
}

const wordsPositionData = [
  { word: 'إِنَّ',         boundingBox: {x1: 1120, y1: 33, x2: 1170, y2: 33, x3: 1171, y3: 120, x4: 1121, y4: 120} },
  { word: 'الَّذِينَ',        boundingBox: {x1: 953, y1: 33, x2: 1122, y2: 32, x3: 1123, y3: 120, x4: 954, y4: 121} },
  { word: 'كَفَرُ',        boundingBox: {x1: 845, y1: 34, x2: 966, y2: 33, x3: 967, y3: 121, x4: 846, y4: 122} },
  { word: 'وا',         boundingBox: {x1: 811, y1: 35, x2: 856, y2: 35, x3: 857, y3: 122, x4: 812, y4: 122} },
  { word: 'سَوَاءٌ',         boundingBox: {x1: 701, y1: 35, x2: 801, y2: 34, x3: 802, y3: 121, x4: 702, y4: 122} },
  { word: 'عَلَيْهِمْ',        boundingBox: {x1: 543, y1: 36, x2: 685, y2: 35, x3: 686, y3: 123, x4: 544, y4: 124} },
  { word: 'ءَ',        boundingBox: {x1: 514, y1: 37, x2: 536, y2: 37, x3: 537, y3: 124, x4: 515, y4: 124} },
  { word: 'أَنذَرْتَهُمْ',        boundingBox: {x1: 313, y1: 37, x2: 508, y2: 36, x3: 509, y3: 124, x4: 314, y4: 125} },
  { word: 'أَمْ',         boundingBox: {x1: 264, y1: 38, x2: 305, y2: 38, x3: 306, y3: 125, x4: 265, y4: 125} },
  { word: 'لَمْ',         boundingBox: {x1: 224, y1: 39, x2: 261, y2: 39, x3: 262, y3: 126, x4: 225, y4: 126} },
  { word: 'تُنذِرْهُمْ',         boundingBox: {x1: 91, y1: 39, x2: 210, y2: 38, x3: 211, y3: 126, x4: 92, y4: 127} },
  { word: 'لَا',         boundingBox: {x1: 1136, y1: 166, x2: 1175, y2: 166, x3: 1176, y3: 251, x4: 1137, y4: 251} },
  { word: 'يُؤْمِنُونَ',         boundingBox: {x1: 984, y1: 166, x2: 1125, y2: 165, x3: 1126, y3: 251, x4: 985, y4: 252} },
  { word: 'خَتَمَ',        boundingBox: {x1: 766, y1: 168, x2: 859, y2: 167, x3: 860, y3: 253, x4: 767, y4: 254} },
  { word: 'اللَّهُ',         boundingBox: {x1: 698, y1: 169, x2: 777, y2: 168, x3: 778, y3: 253, x4: 699, y4: 254} },
  { word: 'عَلَى',        boundingBox: {x1: 632, y1: 170, x2: 691, y2: 170, x3: 692, y3: 255, x4: 633, y4: 255} },
  { word: 'قُلُوبِهِمْ',         boundingBox: {x1: 471, y1: 170, x2: 618, y2: 169, x3: 619, y3: 255, x4: 472, y4: 256} },
  { word: 'وَعَلَى',         boundingBox: {x1: 377, y1: 172, x2: 460, y2: 171, x3: 461, y3: 256, x4: 378, y4: 257} },
  { word: 'سَمْعِهِمْ',        boundingBox: {x1: 192, y1: 173, x2: 367, y2: 172, x3: 368, y3: 258, x4: 193, y4: 259} },
  { word: 'وَعَلَى',         boundingBox: {x1: 95, y1: 174, x2: 181, y2: 173, x3: 182, y3: 259, x4: 96, y4: 260} },
  { word: 'أَبْصَرِهِمْ',         boundingBox: {x1: 1001, y1: 295, x2: 1168, y2: 295, x3: 1168, y3: 384, x4: 1001, y4: 384} },
  { word: 'غِشَوَةٌ',         boundingBox: {x1: 855, y1: 295, x2: 987, y2: 295, x3: 987, y3: 384, x4: 855, y4: 384} },
  { word: 'وَلَهُمْ',         boundingBox: {x1: 725, y1: 295, x2: 836, y2: 295, x3: 836, y3: 384, x4: 725, y4: 384} },
  { word: 'عَذَابٌ',         boundingBox: {x1: 569, y1: 295, x2: 714, y2: 295, x3: 714, y3: 384, x4: 569, y4: 384} },
  { word: 'عَظِيمٌ',         boundingBox: {x1: 418, y1: 295, x2: 569, y2: 295, x3: 569, y3: 384, x4: 418, y4: 384} },
  { word: 'وَمِنَ',        boundingBox: {x1: 212, y1: 295, x2: 289, y2: 295, x3: 289, y3: 384, x4: 212, y4: 384} },
  { word: 'النَّاسِ',        boundingBox: {x1: 82, y1: 295, x2: 226, y2: 295, x3: 226, y3: 384, x4: 82, y4: 384} },
  { word: 'مَن',         boundingBox: {x1: 1117, y1: 426, x2: 1175, y2: 426, x3: 1175, y3: 511, x4: 1117, y4: 511} },
  { word: 'يَقُولُ',         boundingBox: {x1: 1008, y1: 426, x2: 1112, y2: 426, x3: 1112, y3: 511, x4: 1008, y4: 511} },
  { word: 'ءَامَنَّا',        boundingBox: {x1: 886, y1: 426, x2: 992, y2: 426, x3: 992, y3: 511, x4: 886, y4: 511} },
  { word: 'بِاللَّهِ',        boundingBox: {x1: 793, y1: 426, x2: 880, y2: 426, x3: 880, y3: 511, x4: 793, y4: 511} },
  { word: 'وَبِالْيَوْمِ',        boundingBox: {x1: 638, y1: 426, x2: 779, y2: 426, x3: 779, y3: 511, x4: 638, y4: 511} },
  { word: 'الْآخِرِ',        boundingBox: {x1: 510, y1: 426, x2: 632, y2: 426, x3: 632, y3: 511, x4: 510, y4: 511} },
  { word: 'وَمَا',        boundingBox: {x1: 433, y1: 426, x2: 508, y2: 426, x3: 508, y3: 511, x4: 433, y4: 511} },
  { word: 'هُم',         boundingBox: {x1: 379, y1: 426, x2: 428, y2: 426, x3: 428, y3: 511, x4: 379, y4: 511} },
  { word: 'بِمُؤْمِنِينَ',        boundingBox: {x1: 196, y1: 426, x2: 361, y2: 426, x3: 361, y3: 511, x4: 196, y4: 511} },
  { word: 'يُخَدِعُونَ',         boundingBox: {x1: 987, y1: 555, x2: 1160, y2: 555, x3: 1160, y3: 643, x4: 987, y4: 643} },
  { word: 'اللَّهَ',         boundingBox: {x1: 903, y1: 555, x2: 981, y2: 555, x3: 981, y3: 643, x4: 903, y4: 643} },
  { word: 'وَالَّذِينَ',         boundingBox: {x1: 763, y1: 555, x2: 892, y2: 555, x3: 892, y3: 643, x4: 763, y4: 643} },
  { word: 'ءَامَنُوا',         boundingBox: {x1: 616, y1: 555, x2: 750, y2: 555, x3: 750, y3: 643, x4: 616, y4: 643} },
  { word: 'وَمَا',        boundingBox: {x1: 539, y1: 555, x2: 598, y2: 555, x3: 598, y3: 643, x4: 539, y4: 643} },
  { word: 'يَخْدَعُونَ',         boundingBox: {x1: 341, y1: 555, x2: 521, y2: 555, x3: 521, y3: 643, x4: 341, y4: 643} },
  { word: 'إِلَّا',        boundingBox: {x1: 258, y1: 555, x2: 320, y2: 555, x3: 320, y3: 643, x4: 258, y4: 643} },
  { word: 'أَنفُسَهُمْ',         boundingBox: {x1: 92, y1: 555, x2: 251, y2: 555, x3: 251, y3: 643, x4: 92, y4: 643} },
  { word: 'وَمَا',        boundingBox: {x1: 1099, y1: 686, x2: 1165, y2: 686, x3: 1165, y3: 776, x4: 1099, y4: 776} },
  { word: 'يَشْعُرُونَ',         boundingBox: {x1: 948, y1: 686, x2: 1090, y2: 686, x3: 1090, y3: 776, x4: 948, y4: 776} },
  { word: 'فِي',         boundingBox: {x1: 773, y1: 686, x2: 812, y2: 686, x3: 812, y3: 776, x4: 773, y4: 776} },
  { word: 'قُلُوبِهِم',         boundingBox: {x1: 610, y1: 686, x2: 757, y2: 686, x3: 757, y3: 776, x4: 610, y4: 776} },
  { word: 'مَّرَضٌ',        boundingBox: {x1: 480, y1: 686, x2: 605, y2: 686, x3: 605, y3: 776, x4: 480, y4: 776} },
  { word: 'فَزَادَهُمُ',         boundingBox: {x1: 312, y1: 686, x2: 487, y2: 686, x3: 487, y3: 776, x4: 312, y4: 776} },
  { word: 'اللَّهُ',         boundingBox: {x1: 241, y1: 686, x2: 324, y2: 686, x3: 324, y3: 776, x4: 241, y4: 776} },
  { word: 'مَرَضًا',         boundingBox: {x1: 92, y1: 686, x2: 232, y2: 686, x3: 232, y3: 776, x4: 92, y4: 776} },
  { word: 'وَلَهُمْ',         boundingBox: {x1: 1062, y1: 809, x2: 1169, y2: 809, x3: 1169, y3: 902, x4: 1062, y4: 902} },
  { word: 'عَذَابٌ',         boundingBox: {x1: 920, y1: 810, x2: 1052, y2: 809, x3: 1052, y3: 901, x4: 920, y4: 902} },
  { word: 'أَلِيمٌ',         boundingBox: {x1: 824, y1: 810, x2: 906, y2: 810, x3: 906, y3: 903, x4: 824, y4: 903} },
  { word: 'بِمَا',        boundingBox: {x1: 743, y1: 811, x2: 805, y2: 811, x3: 805, y3: 903, x4: 743, y4: 903} },
  { word: 'كَانُوا',        boundingBox: {x1: 648, y1: 811, x2: 743, y2: 811, x3: 743, y3: 903, x4: 648, y4: 903} },
  { word: 'يَكْذِبُونَ',         boundingBox: {x1: 468, y1: 811, x2: 639, y2: 810, x3: 639, y3: 903, x4: 468, y4: 904} },
  { word: 'وَإِذَا',         boundingBox: {x1: 257, y1: 813, x2: 335, y2: 813, x3: 335, y3: 905, x4: 257, y4: 905} },
  { word: 'قِيلَ',        boundingBox: {x1: 180, y1: 813, x2: 244, y2: 813, x3: 244, y3: 905, x4: 180, y4: 905} },
  { word: 'لَهُمْ',        boundingBox: {x1: 89, y1: 813, x2: 169, y2: 813, x3: 169, y3: 906, x4: 89, y4: 906} },
  { word: 'لَا',         boundingBox: {x1: 1130, y1: 945, x2: 1174, y2: 945, x3: 1174, y3: 1028, x4: 1130, y4: 1028} },
  { word: 'تُفْسِدُوا',         boundingBox: {x1: 963, y1: 945, x2: 1123, y2: 945, x3: 1123, y3: 1028, x4: 963, y4: 1028} },
  { word: 'فِي',         boundingBox: {x1: 906, y1: 945, x2: 953, y2: 945, x3: 953, y3: 1028, x4: 906, y4: 1028} },
  { word: 'الْأَرْضِ',        boundingBox: {x1: 745, y1: 945, x2: 901, y2: 945, x3: 901, y3: 1028, x4: 745, y4: 1028} },
  { word: 'قَالُوا',        boundingBox: {x1: 646, y1: 945, x2: 737, y2: 945, x3: 737, y3: 1028, x4: 646, y4: 1028} },
  { word: 'إِنَّمَا',         boundingBox: {x1: 558, y1: 945, x2: 636, y2: 945, x3: 636, y3: 1028, x4: 558, y4: 1028} },
  { word: 'نَحْنُ',        boundingBox: {x1: 457, y1: 945, x2: 546, y2: 945, x3: 546, y3: 1028, x4: 457, y4: 1028} },
  { word: 'مُصْلِحُونَ',         boundingBox: {x1: 208, y1: 945, x2: 450, y2: 945, x3: 450, y3: 1028, x4: 208, y4: 1028} },
  { word: 'أَلَا',        boundingBox: {x1: 1098, y1: 1074, x2: 1167, y2: 1075, x3: 1166, y3: 1170, x4: 1097, y4: 1169} },
  { word: 'إِنَّهُمْ',         boundingBox: {x1: 999, y1: 1073, x2: 1095, y2: 1074, x3: 1094, y3: 1170, x4: 998, y4: 1169} },
  { word: 'هُمُ',         boundingBox: {x1: 930, y1: 1073, x2: 983, y2: 1073, x3: 982, y3: 1168, x4: 929, y4: 1168} },
  { word: 'الْمُفْسِدُونَ',         boundingBox: {x1: 707, y1: 1071, x2: 921, y2: 1073, x3: 920, y3: 1169, x4: 706, y4: 1167} },
  { word: 'وَلَكِن',         boundingBox: {x1: 568, y1: 1070, x2: 688, y2: 1071, x3: 687, y3: 1167, x4: 567, y4: 1166} },
  { word: 'لَّا',          boundingBox: {x1: 522, y1: 1070, x2: 561, y2: 1070, x3: 560, y3: 1165, x4: 521, y4: 1165} },
  { word: 'يَشْعُرُونَ',     boundingBox: {x1: 371, y1: 1069, x2: 518, y2: 1070, x3: 517, y3: 1165, x4: 370, y4: 1164} },
  { word: 'وَإِذَا',       boundingBox: {x1: 169, y1: 1067, x2: 241, y2: 1068, x3: 240, y3: 1163, x4: 168, y4: 1162} },
  { word: 'قِيلَ',        boundingBox: {x1: 94, y1: 1067, x2: 168, y2: 1068, x3: 167, y3: 1163, x4: 93, y4: 1162} },
  { word: 'لَهُمْ',        boundingBox: {x1: 1102, y1: 1211, x2: 1174, y2: 1212, x3: 1173, y3: 1300, x4: 1101, y4: 1299} },
  { word: 'عَامِنُوا',     boundingBox: {x1: 958, y1: 1209, x2: 1078, y2: 1210, x3: 1077, y3: 1299, x4: 957, y4: 1298} },
  { word: 'كَمَا',        boundingBox: {x1: 878, y1: 1209, x2: 953, y2: 1210, x3: 952, y3: 1297, x4: 877, y4: 1296} },
  { word: 'ءَامَنَ',       boundingBox: {x1: 776, y1: 1207, x2: 861, y2: 1208, x3: 860, y3: 1296, x4: 775, y4: 1295} },
  { word: 'النَّاسُ',      boundingBox: {x1: 629, y1: 1205, x2: 769, y2: 1207, x3: 768, y3: 1295, x4: 628, y4: 1293} },
  { word: 'قَالُوا',      boundingBox: {x1: 541, y1: 1205, x2: 629, y2: 1206, x3: 628, y3: 1293, x4: 540, y4: 1292} },
  { word: 'أَنُؤْمِنُ',      boundingBox: {x1: 420, y1: 1203, x2: 532, y2: 1204, x3: 531, y3: 1292, x4: 419, y4: 1291} },
  { word: 'كَمَا',        boundingBox: {x1: 350, y1: 1202, x2: 422, y2: 1203, x3: 421, y3: 1291, x4: 349, y4: 1290} },
  { word: 'ءَامَنَ',       boundingBox: {x1: 252, y1: 1201, x2: 339, y2: 1202, x3: 338, y3: 1290, x4: 251, y4: 1289} },
  { word: 'السُّفَهَاءُ',    boundingBox: {x1: 102, y1: 1200, x2: 253, y2: 1202, x3: 252, y3: 1290, x4: 101, y4: 1288} },
  { word: 'أَلَا',         boundingBox: {x1: 1109, y1: 1334, x2: 1172, y2: 1334, x3: 1172, y3: 1429, x4: 1109, y4: 1429} },
  { word: 'إِنَّهُمْ',       boundingBox: {x1: 1012, y1: 1334, x2: 1118, y2: 1334, x3: 1118, y3: 1429, x4: 1012, y4: 1429} },
  { word: 'هُمُ',         boundingBox: {x1: 938, y1: 1334, x2: 993, y2: 1334, x3: 993, y3: 1429, x4: 938, y4: 1429} },
  { word: 'السُّفَهَاءُ',    boundingBox: {x1: 763, y1: 1334, x2: 929, y2: 1334, x3: 929, y3: 1429, x4: 763, y4: 1429} },
  { word: 'وَلَكِن',       boundingBox: {x1: 624, y1: 1334, x2: 743, y2: 1334, x3: 743, y3: 1429, x4: 624, y4: 1429} },
  { word: 'لَّا',          boundingBox: {x1: 578, y1: 1334, x2: 618, y2: 1334, x3: 618, y3: 1429, x4: 578, y4: 1429} },
  { word: 'يَعْلَمُونَ',     boundingBox: {x1: 423, y1: 1334, x2: 569, y2: 1334, x3: 569, y3: 1429, x4: 423, y4: 1429} },
  { word: 'وَإِذَا',       boundingBox: {x1: 199, y1: 1334, x2: 282, y2: 1334, x3: 282, y3: 1429, x4: 199, y4: 1429} },
  { word: 'لَقُوا',       boundingBox: {x1: 92, y1: 1334, x2: 183, y2: 1334, x3: 183, y3: 1429, x4: 92, y4: 1429} },
  { word: 'الَّذِينَ',      boundingBox: {x1: 1071, y1: 1455, x2: 1172, y2: 1454, x3: 1173, y3: 1543, x4: 1072, y4: 1544} },
  { word: 'ءَامَنُواْ',     boundingBox: {x1: 932, y1: 1456, x2: 1050, y2: 1455, x3: 1051, y3: 1544, x4: 933, y4: 1545} },
  { word: 'قَالُوا',      boundingBox: {x1: 828, y1: 1458, x2: 918, y2: 1457, x3: 919, y3: 1545, x4: 829, y4: 1546} },
  { word: 'ءَامَنَّا',      boundingBox: {x1: 709, y1: 1459, x2: 811, y2: 1458, x3: 812, y3: 1546, x4: 710, y4: 1547} },
  { word: 'وَإِذَا',       boundingBox: {x1: 624, y1: 1460, x2: 695, y2: 1459, x3: 696, y3: 1547, x4: 625, y4: 1548} },
  { word: 'خَلَوْا',       boundingBox: {x1: 518, y1: 1460, x2: 608, y2: 1459, x3: 609, y3: 1548, x4: 519, y4: 1549} },
  { word: 'إِلَى',        boundingBox: {x1: 461, y1: 1461, x2: 513, y2: 1461, x3: 514, y3: 1549, x4: 462, y4: 1549} },
  { word: 'شَيَطِينِهِمْ',    boundingBox: {x1: 242, y1: 1462, x2: 447, y2: 1460, x3: 448, y3: 1549, x4: 243, y4: 1551} },
  { word: 'قَالُوا',      boundingBox: {x1: 144, y1: 1464, x2: 231, y2: 1463, x3: 232, y3: 1551, x4: 145, y4: 1552} },
  { word: 'إِنَّا',        boundingBox: {x1: 98, y1: 1464, x2: 144, y2: 1464, x3: 145, y3: 1553, x4: 99, y4: 1553} },
  { word: 'مَعَكُمْ',       boundingBox: {x1: 1064, y1: 1586, x2: 1169, y2: 1584, x3: 1170, y3: 1676, x4: 1065, y4: 1678} },
  { word: 'إِنَّمَا',       boundingBox: {x1: 967, y1: 1587, x2: 1040, y2: 1586, x3: 1041, y3: 1678, x4: 968, y4: 1679} },
  { word: 'نَحْنُ',        boundingBox: {x1: 885, y1: 1589, x2: 966, y2: 1588, x3: 967, y3: 1679, x4: 886, y4: 1680} },
  { word: 'مُسْتَهْزِءُونَ',   boundingBox: {x1: 670, y1: 1590, x2: 879, y2: 1587, x3: 880, y3: 1681, x4: 671, y4: 1684} },
  { word: 'اللَّهُ',          boundingBox: {x1: 475, y1: 1595, x2: 541, y2: 1594, x3: 542, y3: 1686, x4: 476, y4: 1687} },
  { word: 'يَسْتَهْزِئُ',     boundingBox: {x1: 315, y1: 1598, x2: 471, y2: 1596, x3: 472, y3: 1687, x4: 316, y4: 1689} },
  { word: 'بِهِمْ',        boundingBox: {x1: 239, y1: 1599, x2: 301, y2: 1598, x3: 302, y3: 1689, x4: 240, y4: 1690} },
  { word: 'وَيَمُدُّهُمْ',     boundingBox: {x1: 91, y1: 1601, x2: 224, y2: 1599, x3: 225, y3: 1691, x4: 92, y4: 1693} },
  { word: 'فِي',         boundingBox: {x1: 1127, y1: 1723, x2: 1172, y2: 1723, x3: 1171, y3: 1818, x4: 1126, y4: 1818} },
  { word: 'طُغْيَنِهِمْ',     boundingBox: {x1: 953, y1: 1721, x2: 1122, y2: 1723, x3: 1121, y3: 1819, x4: 952, y4: 1817} },
  { word: 'يَعْمَهُونَ',     boundingBox: {x1: 788, y1: 1719, x2: 945, y2: 1721, x3: 944, y3: 1817, x4: 787, y4: 1815} },
  { word: 'أُوْلَيكَ',      boundingBox: {x1: 526, y1: 1716, x2: 666, y2: 1718, x3: 665, y3: 1814, x4: 525, y4: 1812} },
  { word: 'الَّذِينَ',      boundingBox: {x1: 420, y1: 1715, x2: 524, y2: 1716, x3: 523, y3: 1812, x4: 419, y4: 1811} },
  { word: 'اشْتَرَوُا',     boundingBox: {x1: 270, y1: 1713, x2: 412, y2: 1715, x3: 411, y3: 1811, x4: 269, y4: 1809} },
  { word: 'الضَّلَالَةَ',     boundingBox: {x1: 93, y1: 1712, x2: 267, y2: 1714, x3: 266, y3: 1809, x4: 92, y4: 1807} },
  { word: 'بِالْهُدَى',     boundingBox: {x1: 1013, y1: 1850, x2: 1167, y2: 1850, x3: 1167, y3: 1943, x4: 1013, y4: 1943} },
  { word: 'فَمَا',        boundingBox: {x1: 923, y1: 1850, x2: 1000, y2: 1850, x3: 1000, y3: 1943, x4: 923, y4: 1943} },
  { word: 'رَبِحَت',       boundingBox: {x1: 792, y1: 1850, x2: 913, y2: 1850, x3: 913, y3: 1943, x4: 792, y4: 1943} },
  { word: 'تَجَرَتُهُمْ',     boundingBox: {x1: 610, y1: 1850, x2: 790, y2: 1850, x3: 790, y3: 1943, x4: 610, y4: 1943} },
  { word: 'وَمَا',        boundingBox: {x1: 529, y1: 1850, x2: 596, y2: 1850, x3: 596, y3: 1943, x4: 529, y4: 1943} },
  { word: 'كَانُوا',      boundingBox: {x1: 430, y1: 1850, x2: 530, y2: 1850, x3: 530, y3: 1943, x4: 430, y4: 1943} },
  { word: 'مُهْتَدِينَ',     boundingBox: {x1: 213, y1: 1850, x2: 417, y2: 1850, x3: 417, y3: 1943, x4: 213, y4: 1943} },
].map(data => ({
  x1: data.boundingBox.x1 / 1250,
  y1: data.boundingBox.y1 / 2022,
  x2: data.boundingBox.x2 / 1250,
  y2: data.boundingBox.y2 / 2022,
  x3: data.boundingBox.x3 / 1250,
  y3: data.boundingBox.y3 / 2022,
  x4: data.boundingBox.x4 / 1250,
  y4: data.boundingBox.y4 / 2022,
  word: data.word,
}));
</script>

<template>
  <div
    class="max-h-full overflow-hidden flex quran-page-photo-container relative"
    :style="{ '--blur-amount': `${8 + (Math.random() * 2 - 1)}px` }"
  >
    <img
      @load="onImageLoad"
      @error="onImageError"
      class="object-contain pointer-events-none z-10 mix-blend-multiply dark:invert dark:mix-blend-screen"
      :class="{ blurred: isLoading }"
      loading="lazy"
      :src="src"
    />

    <VerseEndSign
      v-for="verse in content"
      :key="`verse-end-sign-${verse.chapter.index}-${verse.index}`"
      :verse="verse"
      :highlight="props.highlightedVerseIndex === verse.indexInQuran"
      :data-verse-end-sign="verse.indexInQuran"
      ref="verseEndSigns"
      @click.stop="() => collapseOtherOpenMenus(verse)"
    />

    <div
      v-if="number === 3"
      v-for="(box, index) in wordsPositionData"
      :key="`word-${index}`"
      class="bg-tavoos-orange absolute w-8 h-8 mix-blend-multiply rounded-lg outline outline-8 outline-orange-400 opacity-0 hover:opacity-100 transition-opacity"
      :style="{
        left:   `${100 * box.x1}%`,
        top:    `${100 * box.y1}%`,
        width:  `${100 * (box.x2 - box.x1)}%`,
        height: `${100 * (box.y4 - box.y1)}%`,
      }"
      :title="box.word"
    ></div>
  </div>
</template>

<style scoped>
.blurred {
  filter: blur(var(--blur-amount));
}

.quran-page-photo-container {
  aspect-ratio: 1250 / 2022;
  transition: filter 0.75s ease-out;
}
</style>
