<script setup lang="ts">
import { ref, Ref, computed, onMounted, registerRuntimeCompiler } from 'vue';

import type { PrayersSchedule } from '@/types.ts';

const props = defineProps<{
  schedule: PrayersSchedule;
}>();

interface AudioItem {
  length: number;
  path: string;
}

const azan: AudioItem = {
  length: 4 * 60 + 17, // Seconds
  path: '',
};

const chandKhordi: AudioItem = {
  length: 5 * 60 + 4, // Seconds
  path: '/sound/pray-times/dua/mohammadreza_shajarian_masnavi_afshari 128 - chand khordi.mp3',
};

const duaEftetah: AudioItem = {
  length: 20 * 60 + 41, // Seconds
  path: '/sound/pray-times/dua/دعاء الافتتاح - علی فانی.mp3',
};

const duaSahar: AudioItem = {
  length: 19 * 60 + 3, // Seconds
  path: '/sound/pray-times/dua/69_Sahar-Mosavi-Qahar.mp3',
};

const rabbana: AudioItem = {
  length: 4 * 60 + 27, // Seconds
  path: '/sound/pray-times/dua/<PERSON><PERSON> Ra<PERSON><PERSON> (UpMusic).mp3',
};

const tavashihAsma: AudioItem = {
  length: 2 * 60 + 56, // Seconds
  path: '/sound/pray-times/dua/اسماء الحسنی - گروه محمد رسول الله.mp3',
};

const audioSource = ref('');
const isPausedByUser = ref(false);
let isAudioElementSetup = false;
const errorMessage = ref('');

const fajrPreSequence = [duaSahar];
const fajrPostSequence = [] as AudioItem[];
const maghrebPreSequence = [chandKhordi, rabbana, tavashihAsma];
const maghrebPostSequence = [duaEftetah];

const audioElement: Ref<HTMLAudioElement | null> = ref(null);

const fajrTime = computed(() => props.schedule.timeTable.fajr);
const maghrebTime = computed(() => props.schedule.timeTable.maghreb);

const timeSinceFajr = ref(100000); // In seconds
const timeToFajr = ref(100000); // In seconds
const timeSinceMaghreb = ref(100000); // In seconds
const timeToMaghreb = ref(100000); // In seconds

const shouldPlayPreFajr = computed(() => timeToFajr.value < 0 && timeToFajr.value > -sequenceLength(fajrPreSequence));
const shouldPlayPostFajr = computed(() => timeSinceFajr.value > azan.length && timeSinceFajr.value < azan.length + sequenceLength(fajrPostSequence));

const shouldPlayPreMaghreb = computed(() => timeToMaghreb.value < 0 && timeToMaghreb.value > -sequenceLength(maghrebPreSequence));
const shouldPlayPostMaghreb = computed(() => timeSinceMaghreb.value >= azan.length && timeSinceMaghreb.value < azan.length + sequenceLength(maghrebPostSequence));

const shouldPlaySomething = computed(() => shouldPlayPreFajr.value || shouldPlayPostFajr.value|| shouldPlayPreMaghreb.value || shouldPlayPostMaghreb.value);

function sequenceLength(sequence: AudioItem[]) {
  return sequence.map(x => x.length).reduce((acc, x) => acc + x, 0);
}

function tick() {
  timeToFajr.value = (+new Date() - +fajrTime.value!) / 1000; // In seconds, negative
  timeSinceFajr.value = (+new Date() - +fajrTime.value!) / 1000; // In seconds, positive

  timeToMaghreb.value = (+new Date() - +maghrebTime.value!) / 1000; // In seconds, negative
  timeSinceMaghreb.value = (+new Date() - +maghrebTime.value!) / 1000; // In seconds, positive

  if (!audioElement.value) return;

  setupAudioElement();

  const element: HTMLAudioElement = audioElement.value;

  if (shouldPlayPreFajr.value) playPreFajr(element);
  else if (shouldPlayPostFajr.value) playPostFajr(element);
  else if (shouldPlayPreMaghreb.value) playPreMaghreb(element);
  else if (shouldPlayPostMaghreb.value) playPostMaghreb(element);
}

function playPreSequence(sequence: AudioItem[], remainingTime: number, element: HTMLAudioElement) {
  const elapsedTime = sequenceLength(sequence) + remainingTime;

  let path = '';
  let sum = 0;
  sequence.forEach((item: AudioItem) => {
    if (path) return;

    if (elapsedTime - sum < item.length) {
      path = item.path;
    } else {
      sum += item.length;
    }
  });

  if (audioSource.value !== path) {
    audioSource.value = path;
  }

  const currentTime = elapsedTime - sum;
  if (Math.abs(element.currentTime - currentTime) > 0.5) {
    element.currentTime = currentTime;
  }
}

function playPostSequence(sequence: AudioItem[], elapsed: number, element: HTMLAudioElement) {
  let path = '';
  let sum = 0;
  sequence.forEach((item: AudioItem) => {
    if (path) return;

    if (elapsed - sum < item.length) {
      path = item.path;
    } else {
      sum += item.length;
    }
  });

  if (audioSource.value !== path) {
    audioSource.value = path;
  }

  const currentTime = elapsed - sum;
  if (Math.abs(element.currentTime - currentTime) > 0.5) {
    element.currentTime = currentTime;
  }
}

function playPreFajr(element: HTMLAudioElement) {
  playPreSequence(fajrPreSequence, timeToFajr.value,element);
}

function playPostFajr(element: HTMLAudioElement) {
  playPostSequence(fajrPostSequence, timeSinceFajr.value - azan.length, element);
}

function playPreMaghreb(element: HTMLAudioElement) {
  playPreSequence(maghrebPreSequence, timeToMaghreb.value, element);
}

function playPostMaghreb(element: HTMLAudioElement) {
  playPostSequence(maghrebPostSequence, timeSinceMaghreb.value - azan.length, element);
}

function setupAudioElement() {
  const element: HTMLAudioElement | null = audioElement.value;

  if (!element || isAudioElementSetup) return;

  // Try playing after ensuring it's loaded
  element.addEventListener('canplaythrough', () => {
    if (isPausedByUser.value) return;

    element.play().catch(error => {
      errorMessage.value = error.name === 'NotAllowedError'
        ? 'Please click somewhere to enable audio playback'
        : 'Something went wrong';
    });
  });

  // Detect when the user manually pauses
  element.addEventListener('pause', () => isPausedByUser.value = true);

  // Reset when user presses play
  element.addEventListener('play', () => {
    isPausedByUser.value = false;
    errorMessage.value = '';
  });

  // If it doesn’t start, wait for user interaction
  document.addEventListener(
    'click',
    () => { if (element.paused && !isPausedByUser.value) element.play() },
    { once: true }
  );
}

onMounted(() => setInterval(tick, 1000))
</script>

<template>
  <audio v-if="shouldPlaySomething"
    controls
    ref="audioElement"
    class="mx-auto mix-blend-lighten h-8 mt-1 mb-8"
    :src="audioSource"
  >
  </audio>
  <div v-else class="m-auto p-1 text-black">
    Ramadan Al-Karim Mubarak
  </div>
  <div v-if="errorMessage" class="m-auto p-1">{{  errorMessage  }}</div>
</template>
